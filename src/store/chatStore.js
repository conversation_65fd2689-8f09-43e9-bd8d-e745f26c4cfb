import { defineStore } from 'pinia'
import knowledgeApi from '@/api/knowledge'
import { streamChatWithKnowledge } from '@/api/chat'
import { useUserStore } from './user'

export const useChatStore = defineStore('chat', {
  state: () => ({
    // 当前对话消息列表
    messages: [],
    
    // 知识库列表
    knowledgeList: [],
    
    // 当前选中的知识库
    currentKnowledge: null,
    
    // 聊天设置
    chatSettings: {
      model_name: 'deepseek-r1:14b',
      temperature: 0.7,
      top_k: 4,
      score_threshold: 1,
      history: '1', // 是否启用历史对话
      max_tokens: 0,
      prompt_name: 'default',
      mix_type: 'bm25,faiss'
    },
    
    // 流式输出状态
    streaming: {
      active: false,
      messageId: null,
      content: ''
    },
    
    // 历史记录
    historyList: [],

    // 常用问题
    commonQuestions: [],
    
    // 加载状态
    isLoading: false,
    isSending: false,
    
    // 请求任务（用于中断）
    requestTask: null
  }),

  getters: {
    // 是否可以发送消息
    canSendMessage: (state) => !state.isSending && !state.streaming.active,
    
    // 是否正在流式输出
    isStreaming: (state) => state.streaming.active,
    
    // 当前知识库名称
    currentKnowledgeName: (state) => {
      return state.currentKnowledge?.label || ''
    },
    
    // 格式化的历史对话（用于API调用）- 使用PC端相同的对象数组格式
    formattedHistory: (state) => {
      const history = []
      // 修复循环条件，确保能处理所有完整的对话对
      for (let i = 0; i < state.messages.length; i += 2) {
        const userMsg = state.messages[i]
        const aiMsg = state.messages[i + 1]

        // 确保用户消息和AI消息都存在且有内容，且AI消息已完成
        if (userMsg?.type === 'user' &&
            aiMsg?.type === 'assistant' &&
            aiMsg.done &&
            !aiMsg.isStreaming) {
          const userContent = (userMsg.content || userMsg.answer || '').trim()
          const aiContent = (aiMsg.content || aiMsg.answer || '').trim()

          // 只有当两个消息都有内容时才添加到历史记录
          if (userContent && aiContent) {
            // 使用PC端相同的对象格式
            history.push({
              role: 'user',
              content: userContent
            })
            history.push({
              role: 'assistant',
              content: aiContent
            })
          }
        }
      }
      return history
    }
  },

  actions: {
    /**
     * 初始化聊天 - 参考 PC 端逻辑
     */
    async initChat() {
      try {
        this.isLoading = true
        
        // 获取知识库列表
        await this.fetchKnowledgeList()
        
        // 设置默认知识库并加载配置
        if (this.knowledgeList.length > 0) {
          const firstKnowledge = this.knowledgeList[0]
          this.currentKnowledge = firstKnowledge

          // 获取第一个知识库的配置
          if (firstKnowledge.label) {
            await this.fetchChatSettings(firstKnowledge.label)
            // 获取历史对话列表
            await this.fetchHistoryList(firstKnowledge.label)
            // 获取常用问题列表
            await this.fetchCommonQuestions(firstKnowledge.label)
          }
        }
        
      } catch (error) {
        console.error('初始化聊天失败:', error)
        throw error
      } finally {
        this.isLoading = false
      }
    },

    /**
     * 获取知识库列表 - 取 models 和 getKnowledgeSetting 的交集
     */
    async fetchKnowledgeList() {
      try {
        const userStore = useUserStore()
        const nickName = userStore.userInfo.nickName || ''

        // 1. 获取用户的 models 列表
        const modelIds = userStore.userInfo.models || []
        console.log('用户知识库列表 models:', modelIds)

        // 2. 获取知识库配置列表
        const response = await knowledgeApi.getChatSetting()
        console.log('知识库配置列表:', response)

        if (response.code !== 200) {
          throw new Error('获取知识库配置失败')
        }

        const knowledgeSettings = response.data || []

        // 3. 取交集：只保留用户有权限且有配置的知识库
        const knowledgeList = knowledgeSettings
          .filter(setting => {
            // 必须在用户的 models 列表中
            const hasPermission = modelIds.includes(setting.knowledge_name)
            // 过滤个人文档和临时对话
            const isValidKnowledge = !(
              (setting.knowledge_name.startsWith('个人文档-') &&
               setting.knowledge_name !== `个人文档-${nickName || ''}`) ||
              setting.knowledge_name.startsWith('template-')
            )
            return hasPermission && isValidKnowledge
          })
          .map((setting, index) => ({
            label: setting.knowledge_name,
            value: index,
            id: setting.knowledge_name,
            // 直接保存配置信息，避免后续重复请求
            settings: setting,
            // 添加知识库图标信息
            icon: setting.icon || '📚' // 默认图标
          }))

        console.log('最终知识库列表:', knowledgeList)
        this.knowledgeList = knowledgeList

        // 4. 如果有知识库，预设第一个知识库的配置
        if (knowledgeList.length > 0) {
          this.chatSettings = knowledgeList[0].settings
        } else {
          throw new Error('用户没有可用的知识库权限')
        }

      } catch (error) {
        console.error('获取知识库列表失败:', error)
        throw error
      }
    },



    /**
     * 切换知识库 - 使用已缓存的配置
     */
    async switchKnowledge(knowledge) {
      this.currentKnowledge = knowledge

      // 直接使用已缓存的配置，无需重复请求
      if (knowledge.settings) {
        this.chatSettings = knowledge.settings

        // 根据新知识库的历史记录配置决定是否保留现有消息
        const shouldUseHistory = knowledge.settings.history === "1" || knowledge.settings.history === 1
        console.log('知识库历史配置:', knowledge.settings.history, '是否启用历史:', shouldUseHistory)

        // 如果新知识库不启用历史记录，则清除现有消息
        if (!shouldUseHistory) {
          this.messages = []
        }

        // 重新加载新知识库的历史记录
        await this.fetchHistoryList(knowledge.label)

        // 重新加载新知识库的常用问题
        await this.fetchCommonQuestions(knowledge.label)
      } else {
        // 兜底：如果没有缓存配置，则请求获取
        console.warn('知识库配置未缓存，重新获取:', knowledge.label)
        await this.fetchChatSettings(knowledge.label)
      }
    },

    /**
     * 获取聊天设置 - 参考 PC 端 getChatSetting 逻辑
     */
    async fetchChatSettings(knowledgeName) {
      if (!knowledgeName) return

      try {
        const response = await knowledgeApi.getChatSetting({
          knowledgeName: knowledgeName
        })

        if (response.code === 200 && response.data?.length > 0) {
          this.chatSettings = {
            ...response.data[0],
            knowledge_name: knowledgeName
          }
        } else {
          // 使用默认配置
          this.chatSettings = {
            ...this.chatSettings,
            knowledge_name: knowledgeName
          }
        }
      } catch (error) {
        console.error('获取聊天设置失败:', error)
        // 使用默认配置
        this.chatSettings = {
          ...this.chatSettings,
          knowledge_name: knowledgeName
        }
      }
    },

    /**
     * 获取历史对话列表
     */
    async fetchHistoryList(knowledgeName) {
      if (!knowledgeName) return

      try {
        const response = await knowledgeApi.getHistoryList({
          knowledge_name: knowledgeName,  // 修正参数名
          pageNum: 1,
          pageSize: 20,
          generate: "0"  // 添加 generate 参数
        })

        if (response.code === 200) {
          this.historyList = response.rows || []
          console.log('历史对话列表:', this.historyList)
        }
      } catch (error) {
        console.error('获取历史对话列表失败:', error)
      }
    },

    /**
     * 获取常用问题列表
     */
    async fetchCommonQuestions(knowledgeName) {
      if (!knowledgeName) return

      try {
        const response = await knowledgeApi.getCommonQuestions({
          pageNum: 1,
          pageSize: 15,
          knowledgeName: knowledgeName,
          faq: ''
        })

        if (response.code === 200) {
          this.commonQuestions = response.rows || []
          console.log('常用问题列表:', this.commonQuestions)
        }
      } catch (error) {
        console.error('获取常用问题列表失败:', error)
      }
    },

    /**
     * 发送消息
     */
    async sendMessage(text) {
      if (!text.trim() || this.isSending) return
      
      this.isSending = true
      
      try {
        // 添加用户消息
        const userMessage = {
          id: Date.now() + '_user',
          type: 'user',
          content: text.trim(),
          timestamp: new Date().toISOString()
        }
        this.messages.push(userMessage)
        
        // 添加AI消息占位符
        const aiMessage = {
          id: Date.now() + '_ai',
          type: 'assistant',
          content: '',
          timestamp: new Date().toISOString(),
          isStreaming: true,
          done: false
        }
        this.messages.push(aiMessage)
        
        // 开始流式聊天
        await this.startStreamChat(text, aiMessage.id)
        
      } catch (error) {
        console.error('发送消息失败:', error)
        // 移除失败的消息
        this.messages = this.messages.filter(msg => !msg.isStreaming)
        throw error
      } finally {
        this.isSending = false
      }
    },

    /**
     * 开始流式聊天
     */
    async startStreamChat(query, messageId) {
      if (!this.currentKnowledge) {
        throw new Error('请先选择知识库')
      }

      // 准备历史对话数据 - 排除当前正在进行的对话
      const shouldUseHistory = this.chatSettings.history === '1' || this.chatSettings.history === 1
      let historyData = []

      if (shouldUseHistory) {
        // 获取除了最后两条消息（当前用户消息和AI占位消息）之外的历史记录
        const completedMessages = this.messages.slice(0, -2)
        const history = []

        console.log('所有消息:', this.messages.map(m => ({type: m.type, content: m.content || m.answer, done: m.done, isStreaming: m.isStreaming})))
        console.log('已完成消息:', completedMessages.map(m => ({type: m.type, content: m.content || m.answer, done: m.done, isStreaming: m.isStreaming})))

        // 修复循环条件：应该是 i < completedMessages.length，而不是 length - 1
        for (let i = 0; i < completedMessages.length; i += 2) {
          const userMsg = completedMessages[i]
          const aiMsg = completedMessages[i + 1]

          console.log(`检查消息对 ${i/2 + 1}:`, {
            user: userMsg ? {type: userMsg.type, content: userMsg.content || userMsg.answer} : null,
            ai: aiMsg ? {type: aiMsg.type, content: aiMsg.content || aiMsg.answer, done: aiMsg.done, isStreaming: aiMsg.isStreaming} : null
          })

          if (userMsg?.type === 'user' &&
              aiMsg?.type === 'assistant' &&
              aiMsg.done &&
              !aiMsg.isStreaming) {
            const userContent = (userMsg.content || userMsg.answer || '').trim()
            const aiContent = (aiMsg.content || aiMsg.answer || '').trim()

            if (userContent && aiContent) {
              // 使用PC端相同的对象格式
              history.push({
                role: 'user',
                content: userContent
              })
              history.push({
                role: 'assistant',
                content: aiContent
              })
              console.log('添加到历史记录:', {user: userContent, ai: aiContent})
            }
          }
        }
        historyData = history
      }

      console.log('发送请求的历史数据:', historyData)

      const requestParams = {
        query,
        knowledge_base_name: this.currentKnowledge.label,
        stream: true,
        history: historyData,
        model_name: this.chatSettings.model_name,
        temperature: this.chatSettings.temperature,
        top_k: this.chatSettings.top_k,
        score_threshold: this.chatSettings.score_threshold,
        max_tokens: this.chatSettings.max_tokens || 0,
        prompt_name: this.chatSettings.prompt_name || 'default',
        mix_type: this.chatSettings.mix_type || 'bm25,faiss'
      }

      try {
        const response = await streamChatWithKnowledge(requestParams)

        // 保存请求任务以便中断（如果 streamChatWithKnowledge 返回了 requestTask）
        if (response.requestTask) {
          this.requestTask = response.requestTask
        }
        
        if (!response.ok) {
          throw new Error(`请求失败: ${response.status}`)
        }

        const reader = response.body.getReader()
        const decoder = new TextDecoder()
        let buffer = ''

        this.streaming = {
          active: true,
          messageId,
          content: ''
        }

        while (true) {
          const { done, value } = await reader.read()
          
          if (done) break

          buffer += decoder.decode(value, { stream: true })
          const lines = buffer.split('\n')
          buffer = lines.pop() || ''

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6)
              if (data === '[DONE]') continue

              try {
                const parsed = JSON.parse(data)
                if (parsed.answer) {
                  this.streaming.content += parsed.answer
                  this.updateMessageContent(messageId, this.streaming.content)
                }
              } catch (e) {
                console.warn('解析流式数据失败:', e)
              }
            }
          }
        }

        // 完成流式输出
        this.finishStreaming(messageId)

      } catch (error) {
        console.error('流式聊天失败:', error)
        this.streaming.active = false
        throw error
      }
    },

    /**
     * 更新消息内容
     */
    updateMessageContent(messageId, content) {
      const message = this.messages.find(msg => msg.id === messageId)
      if (message) {
        message.content = content
      }
    },

    /**
     * 完成流式输出
     */
    finishStreaming(messageId) {
      const message = this.messages.find(msg => msg.id === messageId)
      if (message) {
        message.isStreaming = false
        message.done = true
      }

      this.streaming = {
        active: false,
        messageId: null,
        content: ''
      }
    },

    /**
     * 停止当前对话
     */
    stopChat() {
      if (this.requestTask) {
        this.requestTask.abort()
        this.requestTask = null
      }

      this.streaming.active = false
      this.isSending = false
    },

    /**
     * 清空对话
     */
    clearMessages() {
      this.messages = []
      this.stopChat()
    },

    /**
     * 开启新对话 - 参考PC端实现
     */
    startNewChat() {
      // 清除消息列表
      this.messages = []

      // 重置流式状态
      this.streaming = {
        active: false,
        messageId: null,
        content: ''
      }

      // 重置发送状态
      this.isSending = false

      // 停止当前对话
      this.stopChat()

      console.log('已开启新对话')
    },

    /**
     * 加载历史对话详情
     */
    async loadHistoryChat(chatId) {
      try {
        const response = await knowledgeApi.getHistoryChat(chatId)
        if (response.code === 200) {
          const chatDetail = response.data

          // 清空当前消息
          this.messages = []

          // 添加历史对话消息
          this.messages.push({
            id: Date.now() + '_user',
            type: 'user',
            content: chatDetail.query,
            timestamp: chatDetail.create_time
          })

          this.messages.push({
            id: Date.now() + '_ai',
            type: 'assistant',
            content: chatDetail.answer,
            timestamp: chatDetail.create_time,
            done: true
          })
        }
      } catch (error) {
        console.error('加载历史对话失败:', error)
        throw error
      }
    }
  }
})
