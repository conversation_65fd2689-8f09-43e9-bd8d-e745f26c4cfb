<script setup lang="ts">
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import { useChatStore } from '@/store/chatStore'
import { useUserStore } from '@/store/user'
import ChatNavbar from './components/ChatNavbar.vue'
import MessageList from './components/MessageList.vue'
import HistoryDrawer from './components/HistoryDrawer.vue'
import BottomPanel from './components/BottomPanel.vue'
import LoadingMask from './components/LoadingMask.vue'

const chatStore = useChatStore()
const userStore = useUserStore()

// 响应式数据
const scrollTop = ref(0)
const showHistory = ref(false)
const statusBarHeight = ref(0)
const safeAreaBottom = ref(0)

// 计算属性
const messages = computed(() => chatStore.messages)
const knowledgeList = computed(() => chatStore.knowledgeList)
const currentKnowledge = computed(() => chatStore.currentKnowledge)
const currentKnowledgeName = computed(() => chatStore.currentKnowledgeName)
const historyList = computed(() => chatStore.historyList)
const commonQuestions = computed(() => chatStore.commonQuestions)
const isLoading = computed(() => chatStore.isLoading)
const isSending = computed(() => chatStore.isSending)

// 获取系统信息
onMounted(async () => {
  // 获取状态栏高度和安全区域
  const systemInfo = uni.getSystemInfoSync()
  statusBarHeight.value = systemInfo.statusBarHeight || 0
  safeAreaBottom.value = systemInfo.safeAreaInsets?.bottom || 0

  // 检查登录状态
  if (!userStore.isLoggedIn) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    })
    uni.reLaunch({
      url: '/pages/login/index'
    })
    return
  }

  // 初始化聊天
  try {
    await chatStore.initChat()
  } catch (error) {
    console.error('初始化聊天失败:', error)
    uni.showToast({
      title: '初始化失败，请重试',
      icon: 'none'
    })
  }
})

// 监听消息变化，自动滚动到底部
watch(messages, () => {
  nextTick(() => {
    scrollToBottom()
  })
}, { deep: true })

// 发送消息
const sendMessage = async (text: string) => {
  if (!text.trim() || isSending.value) return

  try {
    await chatStore.sendMessage(text)
  } catch (error) {
    uni.showToast({
      title: error.message || '发送失败',
      icon: 'none'
    })
  }
}

// 滚动到底部
const scrollToBottom = () => {
  scrollTop.value = 999999
}



// 显示历史列表
const showHistoryList = () => {
  showHistory.value = true
}

// 隐藏历史列表
const hideHistoryList = () => {
  showHistory.value = false
}

// 选择历史对话
const selectHistory = async (history: any) => {
  try {
    // 加载历史对话详情
    await chatStore.loadHistoryChat(history.id)
    hideHistoryList()
    uni.showToast({
      title: '已加载历史对话',
      icon: 'success'
    })
  } catch (error) {
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  }
}

// 选择知识库
const selectKnowledge = async (knowledge: any) => {
  if (currentKnowledge.value?.label === knowledge.label) {
    return
  }

  try {
    await chatStore.switchKnowledge(knowledge)
    uni.showToast({
      title: `已切换到 ${knowledge.label}`,
      icon: 'success'
    })
  } catch (error) {
    uni.showToast({
      title: '切换失败',
      icon: 'none'
    })
  }
}

// 开启新对话
const startNewChat = () => {
  uni.showModal({
    title: '确认操作',
    content: '确定要开启新对话吗？当前对话记录将被清除。',
    success: (res) => {
      if (res.confirm) {
        chatStore.startNewChat()
        uni.showToast({
          title: '已开启新对话',
          icon: 'success'
        })
      }
    }
  })
}
</script>

<template>
  <view class="chat-container">
    <!-- 自定义导航栏 -->
    <ChatNavbar
      :status-bar-height="statusBarHeight"
      :title="currentKnowledgeName || '智能对话'"
      @show-history="showHistoryList"
      @new-chat="startNewChat"
    />

    <!-- 消息列表 -->
    <MessageList
      :messages="messages"
      :scroll-top="scrollTop"
      :current-knowledge-name="currentKnowledgeName"
      :common-questions="commonQuestions"
      :questions-loading="isLoading"
      @select-question="sendMessage"
    />

    <!-- 底部面板：知识库选项卡 + 输入框 -->
    <BottomPanel
      :knowledge-list="knowledgeList"
      :current-knowledge="currentKnowledge"
      :is-sending="isSending"
      :safe-area-bottom="safeAreaBottom"
      @select-knowledge="selectKnowledge"
      @send="sendMessage"
    />

    <!-- 历史对话列表 -->
    <HistoryDrawer
      :show="showHistory"
      :history-list="historyList"
      @close="hideHistoryList"
      @select-history="selectHistory"
    />



    <!-- 加载遮罩 -->
    <LoadingMask :show="isLoading" />
  </view>
</template>

<style scoped lang="scss">
@import './styles/variables.scss';

.chat-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: $bg-color;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

</style>
