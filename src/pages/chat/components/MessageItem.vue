<script setup lang="ts">
interface Message {
  id: string
  type: 'user' | 'assistant'
  content: string
  isStreaming?: boolean
}

interface Props {
  message: Message
  currentKnowledgeName?: string
}

defineProps<Props>()
</script>

<template>
  <view 
    class="message-item"
    :class="{ 'user-message': message.type === 'user', 'ai-message': message.type === 'assistant' }"
  >
    <view class="message-content">
      <!-- 头像 -->
      <view class="avatar" :class="message.type === 'user' ? 'avatar-user' : 'avatar-ai'">
        <text class="avatar-text">{{ message.type === 'user' ? '我' : 'AI' }}</text>
      </view>

      <!-- 消息气泡 -->
      <view class="message-bubble">
        <text class="message-text">{{ message.content }}</text>
        
        <!-- 流式输出动画 -->
        <view v-if="message.isStreaming" class="typing-indicator">
          <text class="typing-dot">●</text>
          <text class="typing-dot">●</text>
          <text class="typing-dot">●</text>
        </view>

        <!-- AI消息下方显示知识库名称 -->
        <view v-if="message.type === 'assistant' && currentKnowledgeName" class="knowledge-tag">
          <text class="knowledge-name">{{ currentKnowledgeName }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
@import '../styles/mixins.scss';

.message-item {
  margin-bottom: $spacing-xl;

  &.user-message .message-content {
    flex-direction: row-reverse;

    .message-bubble {
      background-color: $accent-color;
      color: #FFFFFF;
      margin-left: 80rpx;
    }
  }

  &.ai-message .message-content {
    flex-direction: row;

    .message-bubble {
      background-color: $chat-bubble-bot-bg;
      color: $text-primary;
      border: 1rpx solid $border-color;
      margin-right: 80rpx;
    }
  }
}

.message-content {
  display: flex;
  gap: $spacing-lg;
  align-items: flex-start;
}

.avatar {
  width: $avatar-size;
  height: $avatar-size;
  border-radius: $border-radius-circle;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 500;

  &.avatar-user {
    background: linear-gradient(135deg, $accent-color 0%, #0d47d9 100%);
    color: #FFFFFF;
  }

  &.avatar-ai {
    background: linear-gradient(135deg, #10B981 0%, #06B6D4 50%, #3B82F6 100%);
    color: #FFFFFF;
  }
}

.message-bubble {
  max-width: calc(100% - 144rpx);
  padding: $spacing-lg $spacing-xl;
  border-radius: $border-radius-sm;
  position: relative;
  word-wrap: break-word;
}

.message-text {
  font-size: 30rpx;
  line-height: 1.6;
}

.knowledge-tag {
  margin-top: $spacing-sm;
  padding: $spacing-xs $spacing-sm;
  background-color: rgba(20, 91, 255, 0.1);
  border-radius: $border-radius-md;
  border: 1rpx solid rgba(20, 91, 255, 0.2);
}

.knowledge-name {
  font-size: 24rpx;
  color: $accent-color;
  font-weight: 500;
}

.typing-indicator {
  display: inline-flex;
  align-items: center;
  margin-left: $spacing-sm;
}

.typing-dot {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.6);
  animation: typing 1.4s infinite;

  &:nth-child(2) {
    animation-delay: 0.2s;
  }

  &:nth-child(3) {
    animation-delay: 0.4s;
  }
}

@keyframes typing {
  0%, 60%, 100% {
    opacity: 0.3;
  }
  30% {
    opacity: 1;
  }
}
</style>
